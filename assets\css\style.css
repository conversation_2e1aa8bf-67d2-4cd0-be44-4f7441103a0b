/**
 * MoodifyMe - Main Stylesheet
 */

/* Global Styles */
:root {
    --primary-color: #E55100;
    --secondary-color: #FFC107;
    --accent-color: #FF8F00;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --gray-color: #6c757d;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f5f7fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.lead {
    font-size: 1.25rem;
    font-weight: 300;
}

/* Navbar Customization */
.navbar-brand img {
    margin-right: 10px;
}

.navbar-dark.bg-primary {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color)) !important;
}

/* Main Container */
.main-container {
    padding: 2rem 1rem;
}

/* Welcome Section */
.welcome-section {
    margin: 3rem 0;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cta-buttons {
    margin-top: 2rem;
}

.cta-buttons .btn {
    margin: 0 0.5rem;
    padding: 0.75rem 2rem;
    border-radius: 30px;
}

/* Mood Detection Section */
.mood-detection-section {
    margin: 2rem 0;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mood-input-options {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

.input-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 1.5rem;
    padding: 1rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.input-option:hover {
    background-color: var(--light-color);
    transform: translateY(-5px);
}

.input-option.active {
    background-color: var(--primary-color);
    color: white;
}

.input-option i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.mood-input-form {
    max-width: 600px;
    margin: 0 auto;
}

/* Voice Recorder */
.voice-recorder {
    text-align: center;
    padding: 2rem;
    border: 2px dashed var(--gray-color);
    border-radius: 10px;
}

#recording-status {
    margin-top: 1rem;
    font-style: italic;
}

/* Camera Container */
.camera-container {
    text-align: center;
}

#video {
    border-radius: 10px;
    margin-bottom: 1rem;
    background-color: #eee;
}

#capture-button {
    margin-top: 1rem;
}

/* Facial Landmarks */
.video-container {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

#landmarks-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; /* Allow clicks to pass through to video */
    z-index: 10;
}

/* Facial landmark points animation */
@keyframes pulse {
    0% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); opacity: 0.8; }
}

/* Emotion probability bars */
.emotion-probability-bar {
    height: 10px;
    border-radius: 5px;
    margin-bottom: 5px;
    transition: width 0.3s ease;
}

/* Confidence indicator */
.confidence-indicator {
    position: relative;
    height: 30px;
    border-radius: 15px;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: hidden;
    margin: 10px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.confidence-bar {
    height: 100%;
    border-radius: 15px;
    transition: width 0.5s ease;
}

.confidence-bar.low {
    background-color: rgba(255, 0, 0, 0.8);
}

.confidence-bar.medium {
    background-color: rgba(255, 255, 0, 0.8);
}

.confidence-bar.high {
    background-color: rgba(0, 255, 0, 0.8);
}

.confidence-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
}

/* Recommendation Cards */
.recommendation-card {
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recommendation-card:hover {
    transform: translateY(-5px);
}

.recommendation-card .card-img-top {
    height: 180px;
    object-fit: cover;
}

.recommendation-card .card-body {
    padding: 1.5rem;
}

.recommendation-card .card-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.recommendation-card .badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 0.5rem 0.75rem;
}

/* Emotion History */
.emotion-history-item {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.emotion-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

/* Login & Register Forms */
.auth-form {
    max-width: 450px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.auth-form .form-control {
    padding: 0.75rem;
    border-radius: 5px;
}

.auth-form .btn-primary {
    padding: 0.75rem;
    border-radius: 5px;
}

/* Footer */
footer {
    margin-top: auto;
}

footer .social-icons a {
    font-size: 1.25rem;
    transition: color 0.3s ease;
}

footer .social-icons a:hover {
    color: var(--secondary-color) !important;
}

/* Utility Classes */
.bg-gradient-primary {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.text-gradient {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
