<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="moodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#moodGradient)" filter="url(#shadow)"/>
  
  <!-- Mood face -->
  <!-- Eyes -->
  <circle cx="11" cy="12" r="1.5" fill="#ffffff"/>
  <circle cx="21" cy="12" r="1.5" fill="#ffffff"/>
  
  <!-- Smile -->
  <path d="M 10 18 Q 16 24 22 18" stroke="#ffffff" stroke-width="2" fill="none" stroke-linecap="round"/>
  
  <!-- Subtle highlight -->
  <ellipse cx="13" cy="10" rx="3" ry="2" fill="rgba(255,255,255,0.2)"/>
</svg>
