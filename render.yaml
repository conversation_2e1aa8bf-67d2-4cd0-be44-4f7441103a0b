services:
  # Main PHP Web Application
  - type: web
    name: moodifyme-web
    env: php
    plan: free
    buildCommand: composer install --no-dev --optimize-autoloader
    startCommand: php -S 0.0.0.0:$PORT -t .

    envVars:
      - key: RENDER
        value: "true"
      - key: DATABASE_URL
        fromDatabase:
          name: moodifyme-db
          property: connectionString

# Database
databases:
  - name: moodifyme-db
    databaseName: moodifyme
    user: moodifyme_user
    plan: free
