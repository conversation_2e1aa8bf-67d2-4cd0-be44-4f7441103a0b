
b
$ConstantSidePacketCalculatorNumFacesConstantSidePacketCalculator2PACKET:num_faces:
����

p
+ConstantSidePacketCalculatorRefineLandmarksConstantSidePacketCalculator2PACKET:refine_landmarks:
����
 
OGlScalerCalculatorVIDEO:input_frames_gpu"VIDEO:image_transformed:����( 
�FaceLandmarkFrontGpuIMAGE:image_transformed"LANDMARKS:multi_face_landmarks*NUM_FACES:num_faces*WITH_ATTENTION:refine_landmarks
�!FaceGeometryFromLandmarksAndImageIMAGE:image_transformed/MULTI_FACE_LANDMARKS:multi_face_landmarks_gated"'MULTI_FACE_GEOMETRY:multi_face_geometry
f
EnableFaceGeometryConstantConstantSidePacketCalculator2PACKET:face_geometry_on_packet:
����
 
lSidePacketToStreamCalculatorTICK:multi_face_landmarks"AT_TICK:face_geometry_on*face_geometry_on_packet
ZGateCalculatormulti_face_landmarksALLOW:face_geometry_on"multi_face_landmarks_gatedRinput_frames_gpuXdrApplicationThreadExecutor