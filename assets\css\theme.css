/**
 * MoodifyMe - Custom Theme
 * A visually appealing color scheme and design elements
 */

:root {
    /* Primary Colors - African Sunset Palette */
    --primary-color: #E55100;       /* Burnt Orange - Main brand color */
    --primary-light: #FF8F00;       /* Amber */
    --primary-dark: #D32F2F;        /* Deep Red */

    /* Secondary Colors - Golden/Warm Palette */
    --secondary-color: #FFC107;     /* Golden Yellow - Secondary brand color */
    --secondary-light: #FFD54F;     /* Light Golden */
    --secondary-dark: #FF8F00;      /* Amber */

    /* Accent Colors */
    --accent-1: #8D6E63;            /* Earth Brown */
    --accent-2: #FF8F00;            /* Amber */
    --accent-3: #FFC107;            /* Golden Yellow */

    /* Neutral Colors - Warmer Grays */
    --neutral-50: #faf9f8;          /* Lightest warm gray */
    --neutral-100: #f5f3f2;         /* Very light warm gray */
    --neutral-200: #e8e6e3;         /* Light warm gray */
    --neutral-300: #d1cdc9;         /* Warm gray */
    --neutral-400: #b2aca6;         /* Medium warm gray */
    --neutral-500: #8c8680;         /* Medium-dark warm gray */
    --neutral-600: #6e6862;         /* Dark warm gray */
    --neutral-700: #504a45;         /* Very dark warm gray */
    --neutral-800: #332e2a;         /* Almost black warm */
    --neutral-900: #1c1917;         /* Black warm */

    /* Emotion Colors */
    --happy-color: #facc15;         /* Yellow */
    --sad-color: #3b82f6;           /* Blue */
    --angry-color: #ef4444;         /* Red */
    --anxious-color: #a855f7;       /* Purple */
    --calm-color: #10b981;          /* Green */
    --excited-color: #f97316;       /* Orange */
    --bored-color: #8b5cf6;         /* Violet */
    --tired-color: #6b7280;         /* Gray */
    --stressed-color: #f43f5e;      /* Rose */
    --neutral-color: #64748b;       /* Slate */
    --energetic-color: #fb923c;     /* Orange */
    --focused-color: #0ea5e9;       /* Sky Blue */
    --inspired-color: #22c55e;      /* Green */
    --relaxed-color: #14b8a6;       /* Teal */

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Fonts */
    --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-serif: 'Merriweather', Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: 'Fira Code', Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
}

/* Base Styles */
body {
    font-family: var(--font-sans);
    color: var(--neutral-700);
    background-color: var(--neutral-50);
    line-height: 1.5;
    transition: all 0.3s ease;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    color: var(--neutral-900);
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-dark);
}

/* Navbar Styling */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    letter-spacing: 0.5px;
}

.navbar-brand img {
    height: 40px !important;
    margin-right: 10px;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 1.1rem;
    padding: 0.7rem 1.2rem;
    border-radius: var(--radius);
    transition: all 0.3s ease;
    margin: 0 0.2rem;
}

.navbar-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    font-weight: 600;
}

.navbar-toggler {
    border: none;
    color: white;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Card Styling */
.card {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    background: white;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--neutral-200);
    padding: 1.5rem 2rem;
}

.card-body {
    padding: 2rem;
}

.card-footer {
    background-color: white;
    border-top: 1px solid var(--neutral-200);
    padding: 1.5rem 2rem;
}

.card-title {
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--neutral-800);
    font-size: 1.5rem;
}

.card-subtitle {
    color: var(--neutral-600);
    margin-bottom: 1rem;
    font-weight: 500;
}

/* Special Card Styles */
.card-gradient {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    color: white;
}

.card-gradient .card-title,
.card-gradient .card-subtitle {
    color: white;
}

.card-accent {
    border-left: 4px solid var(--accent-2);
}

.card-hover-reveal .card-hidden-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease;
}

.card-hover-reveal:hover .card-hidden-content {
    max-height: 500px;
}

/* Button Styling */
.btn {
    font-weight: 600;
    padding: 0.7rem 1.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.btn:hover::before {
    transform: translateX(0);
}

.btn:active {
    transform: translateY(2px);
}

.btn-lg {
    padding: 0.9rem 2rem;
    font-size: 1rem;
}

.btn-sm {
    padding: 0.4rem 1rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    box-shadow: 0 4px 10px rgba(142, 68, 173, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    box-shadow: 0 6px 15px rgba(142, 68, 173, 0.4);
    transform: translateY(-3px);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    border: none;
    box-shadow: 0 4px 10px rgba(22, 160, 133, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-light) 0%, var(--secondary-color) 100%);
    box-shadow: 0 6px 15px rgba(22, 160, 133, 0.4);
    transform: translateY(-3px);
}

.btn-accent {
    background: linear-gradient(135deg, var(--accent-2) 0%, var(--accent-1) 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 10px rgba(243, 156, 18, 0.3);
}

.btn-accent:hover {
    background: linear-gradient(135deg, var(--accent-1) 0%, var(--accent-2) 100%);
    box-shadow: 0 6px 15px rgba(243, 156, 18, 0.4);
    transform: translateY(-3px);
    color: white;
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 10px rgba(142, 68, 173, 0.3);
    transform: translateY(-3px);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    color: white;
    box-shadow: 0 4px 10px rgba(22, 160, 133, 0.3);
    transform: translateY(-3px);
}

/* Form Controls */
.form-control {
    border-radius: var(--radius);
    border: 1px solid var(--neutral-300);
    padding: 0.5rem 0.75rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
}

/* Emotion Badges */
.emotion-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-weight: 500;
    color: white;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.emotion-badge i {
    margin-right: 0.5rem;
}

.emotion-badge.happy { background-color: var(--happy-color); }
.emotion-badge.sad { background-color: var(--sad-color); }
.emotion-badge.angry { background-color: var(--angry-color); }
.emotion-badge.anxious { background-color: var(--anxious-color); }
.emotion-badge.calm { background-color: var(--calm-color); }
.emotion-badge.excited { background-color: var(--excited-color); }
.emotion-badge.bored { background-color: var(--bored-color); }
.emotion-badge.tired { background-color: var(--tired-color); }
.emotion-badge.stressed { background-color: var(--stressed-color); }
.emotion-badge.neutral { background-color: var(--neutral-color); }
.emotion-badge.energetic { background-color: var(--energetic-color); }
.emotion-badge.focused { background-color: var(--focused-color); }
.emotion-badge.inspired { background-color: var(--inspired-color); }
.emotion-badge.relaxed { background-color: var(--relaxed-color); }

/* Recommendation Cards */
.recommendation-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.recommendation-card .card-img-top {
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.recommendation-card .card-body {
    flex: 1;
}

.recommendation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.recommendation-actions {
    display: flex;
    gap: 0.5rem;
}

/* Movie Cards */
.movie-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.movie-card .card-img-top {
    height: 300px;
    object-fit: cover;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.slide-up {
    animation: slideUp 0.5s ease forwards;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1.5rem;
    }

    .recommendation-card .card-img-top {
        height: 150px;
    }

    .movie-card .card-img-top {
        height: 200px;
    }
}
